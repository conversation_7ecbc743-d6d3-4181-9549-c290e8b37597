// Test file to verify shared types work in frontend
import { 
  User, 
  Collection, 
  Language, 
  Provider, 
  ApiResponse,
  CreateCollectionRequest,
  WordWithDefinitions,
  PaginatedResponse
} from 'shared-types';

// Test using shared types in frontend context
const testUser: User = {
  id: 'frontend-test-id',
  provider: Provider.TELEGRAM,
  provider_id: 'telegram-123',
  username: null,
  password_hash: null,
  disabled: false,
  created_at: new Date(),
  updated_at: new Date()
};

const testCollection: Collection = {
  id: 'frontend-collection-id',
  name: 'Frontend Test Collection',
  target_language: Language.VI,
  source_language: Language.EN,
  user_id: testUser.id,
  word_ids: ['word1', 'word2'],
  paragraph_ids: ['para1'],
  keyword_ids: ['key1'],
  enable_learn_word_notification: true,
  created_at: new Date(),
  updated_at: new Date()
};

const testApiResponse: ApiResponse<User> = {
  success: true,
  data: testUser,
  message: 'User authenticated successfully'
};

const testPaginatedResponse: PaginatedResponse<Collection> = {
  success: true,
  data: [testCollection],
  pagination: {
    page: 1,
    limit: 10,
    total: 1,
    totalPages: 1
  }
};

const testCreateRequest: CreateCollectionRequest = {
  name: 'Frontend New Collection',
  target_language: Language.VI,
  source_language: Language.EN
};

// Test function that could be used in React components
export const useSharedTypes = () => {
  return {
    user: testUser,
    collection: testCollection,
    apiResponse: testApiResponse,
    paginatedResponse: testPaginatedResponse,
    createRequest: testCreateRequest
  };
};

console.log('✅ Frontend shared types test passed!');
console.log('User Provider:', testUser.provider);
console.log('Collection Language:', testCollection.target_language);
console.log('API Success:', testApiResponse.success);
console.log('Pagination Total:', testPaginatedResponse.pagination?.total);
