// Demo component to test shared types in React
import * as React from 'react';
import type { User, Collection, ApiResponse, CreateCollectionRequest } from 'shared-types';
import { Language, Provider } from 'shared-types';

// Demo component using shared types
const SharedTypesDemo: React.FC = () => {
	// Test using shared types in React component
	const testUser: User = {
		id: 'demo-user-id',
		provider: Provider.USERNAME_PASSWORD,
		provider_id: 'demo-provider-id',
		username: 'demouser',
		password_hash: 'demo-hash',
		disabled: false,
		created_at: new Date(),
		updated_at: new Date(),
	};

	const testCollection: Collection = {
		id: 'demo-collection-id',
		name: 'Demo Collection',
		target_language: Language.EN,
		source_language: Language.VI,
		user_id: testUser.id,
		word_ids: ['word1', 'word2'],
		paragraph_ids: ['para1'],
		keyword_ids: ['key1'],
		enable_learn_word_notification: true,
		created_at: new Date(),
		updated_at: new Date(),
	};

	const testApiResponse: ApiResponse<Collection> = {
		success: true,
		data: testCollection,
		message: 'Collection retrieved successfully',
	};

	const testCreateRequest: CreateCollectionRequest = {
		name: 'New Demo Collection',
		target_language: Language.EN,
		source_language: Language.VI,
	};

	return (
		<div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
			<h1>Shared Types Demo</h1>

			<div style={{ marginBottom: '20px' }}>
				<h2>User Information</h2>
				<p>
					<strong>ID:</strong> {testUser.id}
				</p>
				<p>
					<strong>Username:</strong> {testUser.username}
				</p>
				<p>
					<strong>Provider:</strong> {testUser.provider}
				</p>
				<p>
					<strong>Disabled:</strong> {testUser.disabled ? 'Yes' : 'No'}
				</p>
			</div>

			<div style={{ marginBottom: '20px' }}>
				<h2>Collection Information</h2>
				<p>
					<strong>Name:</strong> {testCollection.name}
				</p>
				<p>
					<strong>Target Language:</strong> {testCollection.target_language}
				</p>
				<p>
					<strong>Source Language:</strong> {testCollection.source_language}
				</p>
				<p>
					<strong>Word Count:</strong> {testCollection.word_ids.length}
				</p>
				<p>
					<strong>Notifications:</strong>{' '}
					{testCollection.enable_learn_word_notification ? 'Enabled' : 'Disabled'}
				</p>
			</div>

			<div style={{ marginBottom: '20px' }}>
				<h2>API Response</h2>
				<p>
					<strong>Success:</strong> {testApiResponse.success ? 'Yes' : 'No'}
				</p>
				<p>
					<strong>Message:</strong> {testApiResponse.message}
				</p>
				<p>
					<strong>Data:</strong> {testApiResponse.data?.name}
				</p>
			</div>

			<div style={{ marginBottom: '20px' }}>
				<h2>Create Request</h2>
				<p>
					<strong>Name:</strong> {testCreateRequest.name}
				</p>
				<p>
					<strong>Target Language:</strong> {testCreateRequest.target_language}
				</p>
				<p>
					<strong>Source Language:</strong> {testCreateRequest.source_language}
				</p>
			</div>

			<div
				style={{
					backgroundColor: '#e8f5e8',
					padding: '15px',
					borderRadius: '5px',
					border: '1px solid #4caf50',
				}}
			>
				<h3 style={{ color: '#2e7d32', margin: '0 0 10px 0' }}>
					✅ Shared Types Test Passed!
				</h3>
				<p style={{ margin: 0, color: '#2e7d32' }}>
					All shared types are working correctly in the React frontend!
				</p>
			</div>
		</div>
	);
};

export default SharedTypesDemo;
