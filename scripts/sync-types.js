#!/usr/bin/env node

/**
 * Script to sync types from Prisma schema to shared-types package
 * This is a helper script to keep shared types in sync with Prisma schema changes
 */

const fs = require('fs');
const path = require('path');

const PRISMA_SCHEMA_PATH = path.join(__dirname, '../backend/prisma/schema.prisma');
const SHARED_TYPES_PATH = path.join(__dirname, '../packages/shared-types/src/index.ts');

console.log('🔄 Syncing types from Prisma schema...');

// Read Prisma schema
const schemaContent = fs.readFileSync(PRISMA_SCHEMA_PATH, 'utf8');

// Extract enums
const enumMatches = schemaContent.match(/enum\s+(\w+)\s*{([^}]+)}/g);
const enums = [];

if (enumMatches) {
  enumMatches.forEach(enumMatch => {
    const nameMatch = enumMatch.match(/enum\s+(\w+)/);
    const valuesMatch = enumMatch.match(/{([^}]+)}/);
    
    if (nameMatch && valuesMatch) {
      const name = nameMatch[1];
      const values = valuesMatch[1]
        .split('\n')
        .map(line => line.trim())
        .filter(line => line && !line.startsWith('//'))
        .map(value => `  ${value} = '${value}'`);
      
      enums.push({
        name,
        values: values.join(',\n')
      });
    }
  });
}

// Extract models
const modelMatches = schemaContent.match(/model\s+(\w+)\s*{([^}]+)}/g);
const models = [];

if (modelMatches) {
  modelMatches.forEach(modelMatch => {
    const nameMatch = modelMatch.match(/model\s+(\w+)/);
    const fieldsMatch = modelMatch.match(/{([^}]+)}/);
    
    if (nameMatch && fieldsMatch) {
      const name = nameMatch[1];
      const fieldsContent = fieldsMatch[1];
      
      // Parse fields (simplified parsing)
      const fields = fieldsContent
        .split('\n')
        .map(line => line.trim())
        .filter(line => line && !line.startsWith('//') && !line.startsWith('@@'))
        .map(line => {
          const parts = line.split(/\s+/);
          if (parts.length >= 2) {
            const fieldName = parts[0];
            let fieldType = parts[1];
            
            // Convert Prisma types to TypeScript types
            if (fieldType === 'String') fieldType = 'string';
            else if (fieldType === 'Int') fieldType = 'number';
            else if (fieldType === 'Boolean') fieldType = 'boolean';
            else if (fieldType === 'DateTime') fieldType = 'Date';
            else if (fieldType.endsWith('[]')) {
              const baseType = fieldType.slice(0, -2);
              if (baseType === 'String') fieldType = 'string[]';
              else if (baseType === 'Int') fieldType = 'number[]';
              else fieldType = `${baseType}[]`;
            }
            
            // Handle optional fields
            const isOptional = fieldType.includes('?') || line.includes('@default');
            const cleanType = fieldType.replace('?', '');
            
            return `  ${fieldName}${isOptional ? '?' : ''}: ${cleanType}${isOptional && !cleanType.includes('null') ? ' | null' : ''};`;
          }
          return null;
        })
        .filter(Boolean);
      
      models.push({
        name,
        fields: fields.join('\n')
      });
    }
  });
}

console.log(`📊 Found ${enums.length} enums and ${models.length} models`);

// Generate TypeScript content
let content = `// Auto-generated types from Prisma schema
// Last updated: ${new Date().toISOString()}
// DO NOT EDIT MANUALLY - Use yarn sync-types to regenerate

`;

// Add enums
enums.forEach(enumDef => {
  content += `export enum ${enumDef.name} {\n${enumDef.values}\n}\n\n`;
});

// Add models
models.forEach(model => {
  content += `export interface ${model.name} {\n${model.fields}\n}\n\n`;
});

// Add custom API types (these should be maintained manually)
content += `// Custom API types (maintained manually)
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Add more custom types as needed...
`;

// Write to shared-types
fs.writeFileSync(SHARED_TYPES_PATH, content);

console.log('✅ Types synced successfully!');
console.log('📝 Remember to build the shared-types package:');
console.log('   yarn workspace shared-types build');
