// Define enums manually to match Prisma schema
export enum Language {
	EN = "EN",
	VI = "VI",
}

export enum Provider {
	TELEGRAM = "TELEGRAM",
	USERNAME_PASSWORD = "USERNAME_PASSWORD",
}

export enum PartsOfSpeech {
	NOUN = "NOUN",
	VERB = "VERB",
	ADJECTIVE = "ADJECTIVE",
	ADVERB = "ADVERB",
	PRONOUN = "PRONOUN",
	PREPOSITION = "PREPOSITION",
	CONJUNCTION = "CONJUNCTION",
	INTERJECTION = "INTERJECTION",
}

export enum Difficulty {
	BEGINNER = "BEGINNER",
	INTERMEDIATE = "INTERMEDIATE",
	ADVANCED = "ADVANCED",
}

export enum Length {
	SHORT = "SHORT",
	MEDIUM = "MEDIUM",
	LONG = "LONG",
}

// Define model types manually to match Prisma schema
export interface User {
	id: string;
	provider: Provider;
	provider_id: string;
	username: string | null;
	password_hash: string | null;
	disabled: boolean;
	created_at: Date;
	updated_at: Date;
}

export interface Word {
	id: string;
	term: string;
	language: Language;
	audio_url: string | null;
	created_at: Date;
	updated_at: Date;
}

export interface Definition {
	id: string;
	word_id: string;
	pos: PartsOfSpeech[];
	ipa: string;
	images: string[];
}

export interface Explain {
	id: string;
	EN: string;
	VI: string;
	definition_id: string;
}

export interface Example {
	id: string;
	EN: string;
	VI: string;
	definition_id: string;
}

export interface Keyword {
	id: string;
	content: string;
	user_id: string;
}

export interface Collection {
	id: string;
	name: string;
	target_language: Language;
	source_language: Language;
	user_id: string;
	word_ids: string[];
	paragraph_ids: string[];
	keyword_ids: string[];
	enable_learn_word_notification: boolean;
	created_at: Date;
	updated_at: Date;
}

export interface LastSeenWord {
	id: string;
	last_seen_at: Date;
	review_count: number;
	user_id: string;
	word_id: string;
}

export interface Paragraph {
	id: string;
	content: string;
	difficulty: Difficulty;
	language: Language;
	length: Length;
}

export interface MultipleChoiceExercise {
	id: string;
	question: string;
	options: string[];
	answer: number;
	explanation: string | null;
	paragraph_id: string;
}

export interface Feedback {
	id: string;
	message: string;
	user_id: string;
	created_at: Date;
}

export interface CollectionStats {
	id: string;
	collection_id: string;
	user_id: string;
	date: Date;
	words_reviewed_count: number;
	qa_practice_submissions: number;
	paragraph_practice_submissions: number;
	created_at: Date;
	updated_at: Date;
}

// Custom types for API responses and requests
export interface ApiResponse<T = any> {
	success: boolean;
	data?: T;
	error?: string;
	message?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
	pagination?: {
		page: number;
		limit: number;
		total: number;
		totalPages: number;
	};
}

// Auth types
export interface LoginRequest {
	username: string;
	password: string;
}

export interface RegisterRequest {
	username: string;
	password: string;
}

export interface AuthResponse {
	user: User;
	token: string;
}

// Collection types
export interface CreateCollectionRequest {
	name: string;
	target_language: Language;
	source_language: Language;
}

export interface UpdateCollectionRequest {
	name?: string;
	target_language?: Language;
	source_language?: Language;
	enable_learn_word_notification?: boolean;
}

// Word types
export interface WordWithDefinitions extends Word {
	definitions: (Definition & {
		explains: Explain[];
		examples: Example[];
	})[];
}

// Stats types
export interface DailyStats {
	date: string;
	words_reviewed_count: number;
	qa_practice_submissions: number;
	paragraph_practice_submissions: number;
}

export interface CollectionStatsResponse {
	collection_id: string;
	daily_stats: DailyStats[];
	total_words_reviewed: number;
	total_qa_submissions: number;
	total_paragraph_submissions: number;
}

// Feedback types
export interface CreateFeedbackRequest {
	message: string;
}

// Paragraph types
export interface ParagraphWithExercises extends Paragraph {
	multiple_choice_exercises: MultipleChoiceExercise[];
}

export interface GenerateParagraphRequest {
	difficulty: Difficulty;
	length: Length;
	language: Language;
	keywords?: string[];
}

// Exercise types
export interface SubmitExerciseRequest {
	exercise_id: string;
	selected_answer: number;
}

export interface ExerciseResult {
	correct: boolean;
	explanation?: string;
	correct_answer: number;
}
