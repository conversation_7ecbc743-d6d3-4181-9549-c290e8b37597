{"name": "vocab-workspace", "version": "1.0.0", "private": true, "workspaces": ["backend", "frontend", "packages/*"], "scripts": {"dev": "yarn workspace vocab-backend dev & yarn workspace vocab-frontend dev", "build": "yarn workspace shared-types build && yarn workspace vocab-backend build && yarn workspace vocab-frontend build", "backend:dev": "yarn workspace vocab-backend dev", "frontend:dev": "yarn workspace vocab-frontend dev", "backend:build": "yarn workspace vocab-backend build", "frontend:build": "yarn workspace vocab-frontend build", "shared:build": "yarn workspace shared-types build", "prisma:generate": "yarn workspace vocab-backend p:g && yarn workspace shared-types build", "prisma:migrate": "yarn workspace vocab-backend p:m", "prisma:studio": "yarn workspace vocab-backend p:s", "install:all": "yarn install"}, "devDependencies": {"typescript": "^5.8.3"}}