// Test file to verify shared types work in backend
import {
	User,
	Collection,
	Language,
	Provider,
	ApiResponse,
	CreateCollectionRequest,
} from '../../packages/shared-types/dist/index.js';

// Test using shared types
const testUser: User = {
	id: 'test-id',
	provider: Provider.USERNAME_PASSWORD,
	provider_id: 'test-provider-id',
	username: 'testuser',
	password_hash: 'hashed-password',
	disabled: false,
	created_at: new Date(),
	updated_at: new Date(),
};

const testCollection: Collection = {
	id: 'collection-id',
	name: 'Test Collection',
	target_language: Language.EN,
	source_language: Language.VI,
	user_id: testUser.id,
	word_ids: [],
	paragraph_ids: [],
	keyword_ids: [],
	enable_learn_word_notification: false,
	created_at: new Date(),
	updated_at: new Date(),
};

const testApiResponse: ApiResponse<Collection> = {
	success: true,
	data: testCollection,
	message: 'Collection retrieved successfully',
};

const testCreateRequest: CreateCollectionRequest = {
	name: 'New Collection',
	target_language: Language.EN,
	source_language: Language.VI,
};

console.log('✅ Backend shared types test passed!');
console.log('User:', testUser.username);
console.log('Collection:', testCollection.name);
console.log('API Response:', testApiResponse.success);
console.log('Create Request:', testCreateRequest.name);
