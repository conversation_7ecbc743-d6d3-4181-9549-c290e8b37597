import { Router, Request, Response } from 'express';
import {
	generateRandomWordsApi,
	generateWordDetailsApi,
	generateParagraph<PERSON><PERSON>,
	generateQuestionsApi,
	generateParagraphWithQuestionsApi,
	evaluateAnswersApi,
	evaluateTranslation<PERSON>pi,
	generateGrammarPracticeApi,
} from '../backend/api/llm.api.js';
import { authenticateToken, AuthenticatedRequest } from '../middleware/auth.js';
import { ValidationError } from '../backend/errors/index.js';

const router = Router();

// All LLM routes require authentication
router.use(authenticateToken);

// Generate random words
router.post('/generate-random-words', async (req: AuthenticatedRequest, res: Response) => {
	try {
		if (!req.user?.id) {
			res.status(401).json({ error: 'Unauthorized' });
			return;
		}

		const { keywordTerms, maxTerms, excludeCollectionIds, source_language, target_language } =
			req.body;
		const words = await generateRandomWordsApi(
			req.user.id,
			keywordTerms || [],
			maxTerms || 10,
			excludeCollectionIds || [],
			source_language,
			target_language
		);
		res.json(words);
	} catch (error) {
		if (error instanceof ValidationError) {
			res.status(400).json({ error: error.message });
			return;
		}
		console.error('Generate random words error:', error);
		res.status(500).json({ error: 'Failed to generate random words' });
	}
});

// Generate word details
router.post('/generate-word-details', async (req: AuthenticatedRequest, res: Response) => {
	try {
		const { terms, source_language, target_language } = req.body;
		const wordDetails = await generateWordDetailsApi(terms, source_language, target_language);
		res.json(wordDetails);
	} catch (error) {
		if (error instanceof ValidationError) {
			res.status(400).json({ error: error.message });
			return;
		}
		console.error('Generate word details error:', error);
		res.status(500).json({ error: 'Failed to generate word details' });
	}
});

// Generate paragraph
router.post('/generate-paragraph', async (req: AuthenticatedRequest, res: Response) => {
	try {
		const { keywords, language, difficulty, count, sentenceCount } = req.body;
		const paragraph = await generateParagraphApi(
			keywords,
			language,
			difficulty,
			count,
			sentenceCount
		);
		res.json(paragraph);
	} catch (error) {
		if (error instanceof ValidationError) {
			res.status(400).json({ error: error.message });
			return;
		}
		console.error('Generate paragraph error:', error);
		res.status(500).json({ error: 'Failed to generate paragraph' });
	}
});

// Generate questions
router.post('/generate-questions', async (req: AuthenticatedRequest, res: Response) => {
	try {
		const data = req.body;
		const questions = await generateQuestionsApi(data);
		res.json(questions);
	} catch (error) {
		if (error instanceof ValidationError) {
			res.status(400).json({ error: error.message });
			return;
		}
		console.error('Generate questions error:', error);
		res.status(500).json({ error: 'Failed to generate questions' });
	}
});

// Generate paragraph with questions
router.post(
	'/generate-paragraph-with-questions',
	async (req: AuthenticatedRequest, res: Response) => {
		try {
			const data = req.body;
			const result = await generateParagraphWithQuestionsApi(data);
			res.json(result);
		} catch (error) {
			if (error instanceof ValidationError) {
				res.status(400).json({ error: error.message });
				return;
			}
			console.error('Generate paragraph with questions error:', error);
			res.status(500).json({ error: 'Failed to generate paragraph with questions' });
		}
	}
);

// Evaluate answers
router.post('/evaluate-answers', async (req: AuthenticatedRequest, res: Response) => {
	try {
		const data = req.body;
		const evaluation = await evaluateAnswersApi(data);
		res.json(evaluation);
	} catch (error) {
		if (error instanceof ValidationError) {
			res.status(400).json({ error: error.message });
			return;
		}
		console.error('Evaluate answers error:', error);
		res.status(500).json({ error: 'Failed to evaluate answers' });
	}
});

// Evaluate translation
router.post('/evaluate-translation', async (req: AuthenticatedRequest, res: Response) => {
	try {
		const data = req.body;
		const evaluation = await evaluateTranslationApi(data);
		res.json(evaluation);
	} catch (error) {
		if (error instanceof ValidationError) {
			res.status(400).json({ error: error.message });
			return;
		}
		console.error('Evaluate translation error:', error);
		res.status(500).json({ error: 'Failed to evaluate translation' });
	}
});

// Grammar practice
router.post('/grammar-practice', async (req: AuthenticatedRequest, res: Response) => {
	try {
		const data = req.body;
		const practice = await generateGrammarPracticeApi(data);
		res.json(practice);
	} catch (error) {
		if (error instanceof ValidationError) {
			res.status(400).json({ error: error.message });
			return;
		}
		console.error('Grammar practice error:', error);
		res.status(500).json({ error: 'Failed to generate grammar practice' });
	}
});

export { router as llmRouter };
