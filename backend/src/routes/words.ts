import { Router, Request, Response } from 'express';
import { searchWordsApi, getWordsByIdsApi } from '../backend/api/word.api.js';
import { saveLastSeenWordApi } from '../backend/api/last-seen-word.api.js';
import { authenticateToken, AuthenticatedRequest, optionalAuth } from '../middleware/auth.js';
import { ValidationError, NotFoundError } from '../backend/errors/index.js';

const router = Router();

// Search words (requires authentication and collection ID)
router.get('/search', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
	try {
		const { collectionId, term, language, limit } = req.query;

		if (!collectionId || !term) {
			res.status(400).json({ error: 'Collection ID and search term are required' });
			return;
		}

		if (!req.user?.id) {
			res.status(401).json({ error: 'Unauthorized' });
			return;
		}

		const words = await searchWordsApi(
			req.user.id,
			collectionId as string,
			term as string,
			language as any,
			limit ? parseInt(limit as string) : 10
		);

		res.json(words);
	} catch (error) {
		if (error instanceof ValidationError) {
			res.status(400).json({ error: error.message });
			return;
		}
		console.error('Search words error:', error);
		res.status(500).json({ error: 'Failed to search words' });
	}
});

// Get words by IDs
router.post('/by-ids', async (req: Request, res: Response) => {
	try {
		const { wordIds } = req.body;

		if (!wordIds || !Array.isArray(wordIds)) {
			res.status(400).json({ error: 'Word IDs array is required' });
			return;
		}

		const words = await getWordsByIdsApi(wordIds);
		res.json(words);
	} catch (error) {
		if (error instanceof ValidationError) {
			res.status(400).json({ error: error.message });
			return;
		}
		console.error('Get words by IDs error:', error);
		res.status(500).json({ error: 'Failed to get words' });
	}
});

// Word details endpoint not implemented yet

// Save last seen word (requires authentication)
router.post(
	'/:id/last-seen',
	authenticateToken,
	async (req: AuthenticatedRequest, res: Response) => {
		try {
			const { id: wordId } = req.params;

			if (!req.user?.id) {
				res.status(401).json({ error: 'Unauthorized' });
				return;
			}

			await saveLastSeenWordApi(req.user.id, wordId);

			res.json({ success: true, message: 'Last seen word saved' });
		} catch (error) {
			if (error instanceof ValidationError) {
				res.status(400).json({ error: error.message });
				return;
			}
			console.error('Save last seen word error:', error);
			res.status(500).json({ error: 'Failed to save last seen word' });
		}
	}
);

export { router as wordsRouter };
