{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": false, "esModuleInterop": true, "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "shared-types": ["../packages/shared-types/dist/index"]}, "outDir": "./dist", "rootDir": "./src"}, "include": ["src/**/*.ts", "src/**/*.js"], "exclude": ["node_modules", "dist"]}