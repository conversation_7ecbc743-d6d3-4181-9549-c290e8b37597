# Yarn Workspace Setup với Shared Types

## 📋 Tổng quan

Dự án đã được thiết lập thành công với **Yarn Workspace** để chia sẻ types giữa backend và frontend, đảm bảo type safety và đồng bộ dữ liệu.

## 🏗️ Cấu trúc dự án

```
vocab/
├── package.json                 # Root workspace config
├── backend/                     # Node.js/Express backend
│   ├── package.json
│   ├── tsconfig.json           # Có path mapping cho shared-types
│   └── src/
├── frontend/                    # React frontend
│   ├── package.json
│   ├── tsconfig.app.json       # Có path mapping cho shared-types
│   └── src/
├── packages/
│   └── shared-types/           # Package chia sẻ types
│       ├── package.json
│       ├── tsconfig.json
│       ├── src/index.ts        # Định nghĩa types
│       ├── dist/               # Compiled output
│       └── README.md
└── scripts/
    └── sync-types.js           # Script đồng bộ types từ Prisma
```

## 🚀 Cách sử dụng

### Cài đặt dependencies

```bash
yarn install
```

### Development

```bash
# Start cả backend và frontend
yarn dev

# Hoặc riêng lẻ
yarn backend:dev
yarn frontend:dev
```

### Build

```bash
# Build tất cả
yarn build

# Build riêng lẻ
yarn backend:build
yarn frontend:build
yarn shared:build
```

### Prisma operations

```bash
# Generate Prisma client và build shared-types
yarn prisma:generate

# Database migration
yarn prisma:migrate

# Prisma Studio
yarn prisma:studio
```

### Sync types từ Prisma schema

```bash
yarn sync-types
```

## 💡 Cách import types

### Backend (Node.js)

```typescript
import { User, Collection, Language, ApiResponse } from "shared-types";
```

### Frontend (React)

```typescript
// Type-only imports
import type { User, Collection, ApiResponse } from "shared-types";

// Value imports (enums)
import { Language, Provider } from "shared-types";
```

## 🔧 Scripts có sẵn

| Script                 | Mô tả                                |
| ---------------------- | ------------------------------------ |
| `yarn dev`             | Start backend + frontend             |
| `yarn build`           | Build tất cả packages                |
| `yarn prisma:generate` | Generate Prisma + build shared-types |
| `yarn sync-types`      | Sync types từ Prisma schema          |
| `yarn shared:build`    | Build shared-types package           |

## 📝 Lưu ý quan trọng

### ✅ Ưu điểm

- **Type Safety**: Đảm bảo types đồng bộ giữa backend và frontend
- **DRY Principle**: Không duplicate type definitions
- **Browser Compatible**: Shared-types không phụ thuộc Node.js runtime
- **Development Experience**: IntelliSense và auto-completion hoạt động tốt
- **Maintainable**: Dễ dàng thêm/sửa types ở một nơi

### ⚠️ Lưu ý

- **Manual Sync**: Types được định nghĩa manual, cần sync khi Prisma schema thay đổi
- **Build Required**: Phải build shared-types sau khi thay đổi
- **No Prisma Client**: Frontend không thể sử dụng Prisma Client trực tiếp

### 🔄 Workflow khi thay đổi database schema

1. Cập nhật `backend/prisma/schema.prisma`
2. Chạy `yarn prisma:generate` (tự động build shared-types)
3. Hoặc chạy `yarn sync-types` để auto-sync types
4. Kiểm tra và cập nhật custom types nếu cần

## 🎯 Kết quả

✅ **Thành công thiết lập yarn workspace với shared types**

- Backend và frontend đều có thể import types từ `shared-types`
- Type safety được đảm bảo across toàn bộ application
- Development experience được cải thiện đáng kể
- Codebase dễ maintain và scale hơn

## 📚 Tài liệu tham khảo

- [Yarn Workspaces Documentation](https://yarnpkg.com/features/workspaces)
- [TypeScript Path Mapping](https://www.typescriptlang.org/docs/handbook/module-resolution.html#path-mapping)
- [Prisma TypeScript](https://www.prisma.io/docs/concepts/components/prisma-client/working-with-prismaclient/generating-prisma-client#using-custom-output-path)
